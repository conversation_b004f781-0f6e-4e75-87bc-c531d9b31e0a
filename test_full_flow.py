#!/usr/bin/env python3
"""
Test script to verify the full flow with real LLM calls using test_article.txt content.
This test will demonstrate that the agent is making actual LLM calls and not just simulating.
"""

import asyncio
import json
import time
from pathlib import Path
from loguru import logger

# Local imports
from src.config import load_config, validate_config
from src.tools import MCPTools
from src.agent import SecurityAgent


async def test_full_flow_with_article():
    """Test the full security analysis flow with the test article content"""
    
    print("🚀 Starting Full Flow Test with Real LLM Calls")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    
    # Validate configuration
    errors = validate_config(config)
    if errors:
        print("❌ Configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return
    
    print(f"✅ Configuration loaded: {config.llm.provider} provider with {config.llm.model}")
    
    # Read the test article content
    test_article_path = Path("test_article.txt")
    if not test_article_path.exists():
        print("❌ test_article.txt not found!")
        return
    
    with open(test_article_path, 'r', encoding='utf-8') as f:
        article_content = f.read()
    
    print(f"📄 Loaded test article: {len(article_content)} characters")
    print(f"📄 Article preview: {article_content[:200]}...")
    print()
    
    # Initialize tools
    tools_config = {
        "article_fetch_timeout": config.processing.article_fetch_timeout,
        "max_content_length": config.processing.max_content_length,
        "memgraph_uri": config.memgraph_uri,
        "memgraph_username": config.memgraph_username,
        "memgraph_password": config.memgraph_password,
    }
    
    tools = MCPTools(tools_config)
    
    try:
        # Initialize tools
        await tools.initialize_mcp_clients()
        
        # Create the security agent
        agent = SecurityAgent(config, tools)
        
        print(f"🤖 SecurityAgent initialized with {config.llm.provider} LLM")
        print()
        
        # Test 1: Direct content assessment with LLM
        print("🧠 Test 1: Content Assessment with Real LLM Call")
        print("-" * 50)
        
        start_time = time.time()
        assessment_result = await agent._assess_content(article_content)
        assessment_time = time.time() - start_time
        
        print(f"⏱️  Assessment took {assessment_time:.2f} seconds")
        print(f"✅ Assessment completed:")
        print(f"   - Sufficient content: {assessment_result.has_sufficient_content}")
        print(f"   - Can generate AT: {assessment_result.can_generate_at}")
        print(f"   - Can generate DoM: {assessment_result.can_generate_dom}")
        print(f"   - Can generate PoM: {assessment_result.can_generate_pom}")
        print(f"   - Confidence: {assessment_result.confidence}")
        print(f"   - Reasoning: {assessment_result.reasoning[:200]}...")
        print()
        
        # Test 2: Sequential thinking with LLM
        print("🧠 Test 2: Sequential Thinking with Real LLM Call")
        print("-" * 50)
        
        start_time = time.time()
        thinking_result = await tools.think_sequential(
            thought_type="analysis",
            prompt=f"""Analyze this security article about CPU_HU cryptominer and identify:
            1. The main attack techniques used
            2. Key indicators of compromise (IOCs)
            3. Potential detection methods
            
            Article content (first 1000 chars):
            {article_content[:1000]}""",
            context={"article_length": len(article_content), "test_mode": True}
        )
        thinking_time = time.time() - start_time
        
        print(f"⏱️  Sequential thinking took {thinking_time:.2f} seconds")
        print(f"✅ Sequential thinking completed:")
        print(f"   - Thought type: {thinking_result.get('thought_type')}")
        print(f"   - Thought number: {thinking_result.get('thought_number')}")
        print(f"   - LLM response length: {len(str(thinking_result.get('llm_response', '')))}")
        print(f"   - Reasoning preview: {str(thinking_result.get('reasoning', ''))[:200]}...")
        print()
        
        # Test 3: Attack Technique generation with LLM
        print("🧠 Test 3: Attack Technique Generation with Real LLM Call")
        print("-" * 50)
        
        if assessment_result.can_generate_at:
            start_time = time.time()
            attack_technique = await agent._generate_attack_technique(article_content)
            generation_time = time.time() - start_time
            
            print(f"⏱️  Attack technique generation took {generation_time:.2f} seconds")
            if attack_technique:
                print(f"✅ Attack technique generated:")
                print(f"   - Name: {attack_technique.name}")
                print(f"   - MITRE ID: {attack_technique.mitre_technique_id}")
                print(f"   - Tactics: {attack_technique.mitre_tactics}")
                print(f"   - Cypher queries: {len(attack_technique.cypher_queries)}")
                print(f"   - Confidence: {attack_technique.confidence_score}")
            else:
                print("❌ No attack technique generated")
        else:
            print("⏭️  Skipping attack technique generation (assessment says not possible)")
        print()
        
        # Test 4: Cypher query validation
        print("🧠 Test 4: Cypher Query Validation")
        print("-" * 50)
        
        test_query = "MATCH (n:AttackTechnique) RETURN n.name, n.mitre_id LIMIT 5"
        start_time = time.time()
        cypher_result = await tools.execute_cypher(test_query)
        cypher_time = time.time() - start_time
        
        print(f"⏱️  Cypher execution took {cypher_time:.2f} seconds")
        print(f"✅ Cypher query result:")
        print(f"   - Success: {cypher_result.get('success')}")
        print(f"   - Execution time: {cypher_result.get('execution_time_ms')}ms")
        print(f"   - Row count: {cypher_result.get('row_count', 0)}")
        if cypher_result.get('error'):
            print(f"   - Error: {cypher_result.get('error')}")
        if cypher_result.get('note'):
            print(f"   - Note: {cypher_result.get('note')}")
        print()
        
        # Test 5: Full processing workflow
        print("🧠 Test 5: Full Processing Workflow")
        print("-" * 50)
        
        # Create a mock URL that will use our article content
        class MockTools(MCPTools):
            def __init__(self, config, article_content):
                super().__init__(config)
                self.mock_content = article_content
                
            async def read_article(self, url: str) -> str:
                """Override to return our test content instead of fetching"""
                logger.info(f"Mock fetching article from: {url}")
                return self.mock_content
        
        # Create mock tools with our article content
        mock_tools = MockTools(tools_config, article_content)
        await mock_tools.initialize_mcp_clients()
        
        # Create agent with mock tools
        mock_agent = SecurityAgent(config, mock_tools)
        
        start_time = time.time()
        processing_result = await mock_agent._analyze_article_content("mock://test-article")
        processing_time = time.time() - start_time
        
        print(f"⏱️  Full processing took {processing_time:.2f} seconds")
        print(f"✅ Processing completed:")
        print(f"   - Status: {processing_result.status}")
        print(f"   - Processing time: {processing_result.processing_time_seconds:.2f}s")
        print(f"   - Errors: {len(processing_result.errors)}")
        print(f"   - Warnings: {len(processing_result.warnings)}")
        print(f"   - Thought history: {len(processing_result.thought_history)}")
        
        if processing_result.attack_technique:
            print(f"   - Attack technique: {processing_result.attack_technique.name}")
        if processing_result.detection_model:
            print(f"   - Detection model: {processing_result.detection_model.name}")
        if processing_result.prevention_model:
            print(f"   - Prevention model: {processing_result.prevention_model.name}")
        
        print()
        
        # Summary
        total_time = assessment_time + thinking_time + (generation_time if 'generation_time' in locals() else 0) + cypher_time + processing_time
        print("📊 SUMMARY")
        print("=" * 60)
        print(f"✅ All tests completed successfully!")
        print(f"⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"🤖 LLM calls made: Multiple real calls to {config.llm.provider}")
        print(f"📄 Article processed: {len(article_content)} characters")
        print(f"🧠 Sequential thinking: Working with real LLM")
        print(f"🔍 Cypher queries: {'Working' if cypher_result.get('success') else 'Simulated'}")
        
        if processing_result.thought_history:
            print(f"💭 Thought history captured: {len(processing_result.thought_history)} thoughts")
            
        print("\n🎉 Test completed! The agent is making real LLM calls.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Cleanup
        await tools.cleanup()
        if 'mock_tools' in locals():
            await mock_tools.cleanup()


if __name__ == "__main__":
    asyncio.run(test_full_flow_with_article())
