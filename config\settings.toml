[llm]
# Provider options: "openai", "anthropic", "azure"
provider = "anthropic"

# Model configuration (provider-specific)
# OpenAI models: "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"
# Anthropic models: "claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"
# Azure models: Use your deployment name
model = "claude-3-5-sonnet-latest"

temperature = 0.2
max_tokens = 4000
retry_attempts = 3

# Azure-specific settings (only needed if provider = "azure")
azure_endpoint = ""  # Set via AZURE_OPENAI_ENDPOINT env var
azure_api_version = "2024-02-15-preview"
azure_deployment_name = ""  # Set via AZURE_OPENAI_DEPLOYMENT_NAME env var

# Example configurations for different providers:
#
# For OpenAI:
# provider = "openai"
# model = "gpt-4"
# (Set OPENAI_API_KEY in .env)
#
# For Anthropic:
# provider = "anthropic"
# model = "claude-3-5-sonnet-20241022"
# (Set ANTHROPIC_API_KEY in .env)
#
# For Azure OpenAI:
# provider = "azure"
# model = "gpt-4"  # or use azure_deployment_name
# azure_endpoint = "https://your-resource.openai.azure.com/"
# azure_deployment_name = "your-gpt4-deployment"
# (Set AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT in .env)

[logging]
level = "INFO"
format = "{time} | {level} | {message}"
rotation = "10 MB"

[mcp]
sequential_thinking_path = "npx"
sequential_thinking_args = ["-y", "@modelcontextprotocol/server-sequential-thinking"]
memgraph_path = "python"
memgraph_args = ["memgraph-mcp-server", "--url", "${MEMGRAPH_URI}"]

[processing]
max_enrichment_rounds = 3
enrichment_timeout = 30
article_fetch_timeout = 60
max_content_length = 50000

[output]
definitions_filename_pattern = "definitions_{timestamp}.json"
state_log_filename_pattern = "state_log_{timestamp}.json"
timestamp_format = "%Y%m%d_%H%M%S"

[validation]
cypher_timeout = 5000
max_validation_retries = 3

[schemas]
# Example Attack Technique schema in YAML format
attack_technique_example = """
name: "Phishing Email Attack"
description: "Social engineering attack using deceptive emails to steal credentials or install malware"
mitre_tactics:
  - "Initial Access"
  - "Credential Access"
mitre_technique_id: "T1566.001"
prerequisites:
  - "Email access to target organization"
  - "Convincing email template"
  - "Credential harvesting infrastructure"
target_systems:
  - "Windows"
  - "macOS"
  - "Linux"
  - "Mobile devices"
cypher_queries:
  - name: "Find related phishing techniques"
    description: "Query to find other phishing-related attack techniques"
    query: "MATCH (t:Technique) WHERE t.name CONTAINS 'phishing' OR t.description CONTAINS 'email' RETURN t.name, t.mitre_id"
  - name: "Find defensive measures"
    description: "Query to find prevention techniques for phishing"
    query: "MATCH (t:Technique)-[:MITIGATED_BY]->(m:Mitigation) WHERE t.mitre_id = 'T1566.001' RETURN m.name, m.description"
confidence_score: 0.9
"""

# Example Detection Model schema in YAML format
detection_model_example = """
name: "Email Attachment Execution Detection"
description: "Detects suspicious execution of email attachments that may indicate phishing attacks"
detection_logic: |
  Monitor for:
  1. Email attachment downloads (.exe, .scr, .bat, .ps1, .doc with macros)
  2. Immediate execution of downloaded files
  3. Network connections from recently downloaded executables
  4. Process creation from email client directories
data_sources:
  - "Email gateway logs"
  - "Endpoint detection and response (EDR)"
  - "Network traffic analysis"
  - "File system monitoring"
detection_type: "real-time"
expected_output: "Alert with attachment hash, sender details, and execution timeline"
false_positive_rate: "Medium - may trigger on legitimate software installations"
confidence_score: 0.85
"""

# Example Prevention Model schema in YAML format
prevention_model_example = """
name: "Multi-layered Phishing Prevention"
description: "Comprehensive prevention strategy combining technical controls and user education"
prevention_steps:
  - "Deploy email security gateway with attachment scanning"
  - "Implement DMARC, SPF, and DKIM email authentication"
  - "Enable safe links and safe attachments in email client"
  - "Conduct regular phishing simulation training"
  - "Implement application whitelisting on endpoints"
  - "Deploy web content filtering"
  - "Enable multi-factor authentication for all accounts"
implementation_complexity: "high"
prerequisites:
  - "Email security solution"
  - "Endpoint protection platform"
  - "User training program"
  - "Identity management system"
target_platforms:
  - "Windows"
  - "macOS"
  - "Linux"
  - "Mobile devices"
  - "Cloud services"
effectiveness_rating: "High - significantly reduces successful phishing attacks"
confidence_score: 0.92
"""
