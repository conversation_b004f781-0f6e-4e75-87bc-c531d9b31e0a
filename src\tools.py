"""MCP tool wrappers for Security Content Analyzer"""

import asyncio
import json
import re
import os
from typing import Dict, Any, Optional, List, TypedDict
from urllib.parse import urljoin, urlparse
import httpx
from bs4 import BeautifulSoup
from loguru import logger

# MCP imports removed - using local Sequential Thinking Tool only
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class ThoughtData(TypedDict, total=False):
    """Type definition for thought data."""

    thought: str
    thoughtNumber: int
    totalThoughts: int
    nextThoughtNeeded: bool
    isRevision: bool
    revisesThought: Optional[int]
    branchFromThought: Optional[int]
    branchId: Optional[str]


class MemgraphConfig(BaseModel):
    """Configuration for Memgraph connection"""

    uri: str = os.getenv("MEMGRAPH_URI", "bolt://localhost:7687")
    username: str = os.getenv("MEMGRAPH_USER", "")
    password: str = os.getenv("MEMGRAPH_PASSWORD", "")


class SequentialThinkingTool:
    """
    Tool for sequential thinking.

    This tool helps the agent break down complex problems into steps,
    analyze issues methodically, and ensure a thorough approach to problem-solving.
    """

    def __init__(self, verbose: bool = True):
        """
        Initialize the sequential thinking tool.

        Args:
            verbose: Whether to print verbose logs
        """
        self.verbose = verbose
        self.thought_history: List[ThoughtData] = []
        self.branches: Dict[str, List[ThoughtData]] = {}
        self.name = "sequential_thinking"
        self.description = """
A tool for breaking down complex problems into steps and analyzing issues methodically.

Parameters explained:
- thought: Your current thinking step, which can include:
  * Regular analytical steps
  * Revisions of previous thoughts
  * Questions about previous decisions
  * Realizations about needing more analysis
  * Changes in approach
  * Hypothesis generation
  * Hypothesis verification
- nextThoughtNeeded: True if you need more thinking, even if at what seemed like the end
- thoughtNumber: Current number in sequence (can go beyond initial total if needed)
- totalThoughts: Current estimate of thoughts needed (can be adjusted up/down)
- isRevision: A boolean indicating if this thought revises previous thinking
- revisesThought: If isRevision is true, which thought number is being reconsidered
"""

    def reset(self):
        """Reset the tool state."""
        self.thought_history = []
        self.branches = {}

    def _validate_input(self, tool_input: Dict[str, Any]) -> ThoughtData:
        """
        Validate the tool input.

        Args:
            tool_input: The input data for the tool

        Returns:
            Validated thought data
        """
        # Basic validation
        required_fields = [
            "thought",
            "thoughtNumber",
            "totalThoughts",
            "nextThoughtNeeded",
        ]
        for field in required_fields:
            if field not in tool_input:
                raise ValueError(f"Missing required field: {field}")

        # Type validation
        if not isinstance(tool_input["thought"], str):
            raise ValueError("thought must be a string")
        if not isinstance(tool_input["thoughtNumber"], int):
            raise ValueError("thoughtNumber must be an integer")
        if not isinstance(tool_input["totalThoughts"], int):
            raise ValueError("totalThoughts must be an integer")
        if not isinstance(tool_input["nextThoughtNeeded"], bool):
            raise ValueError("nextThoughtNeeded must be a boolean")

        # Optional fields validation
        if "isRevision" in tool_input and not isinstance(
            tool_input["isRevision"], bool
        ):
            raise ValueError("isRevision must be a boolean")

        if tool_input.get("isRevision") and "revisesThought" not in tool_input:
            raise ValueError("revisesThought is required when isRevision is true")

        if "revisesThought" in tool_input and not isinstance(
            tool_input["revisesThought"], int
        ):
            raise ValueError("revisesThought must be an integer")

        if "branchFromThought" in tool_input and not isinstance(
            tool_input["branchFromThought"], int
        ):
            raise ValueError("branchFromThought must be an integer")

        if "branchId" in tool_input and not isinstance(tool_input["branchId"], str):
            raise ValueError("branchId must be a string")

        # Convert to ThoughtData
        thought_data: ThoughtData = {
            "thought": tool_input["thought"],
            "thoughtNumber": tool_input["thoughtNumber"],
            "totalThoughts": tool_input["totalThoughts"],
            "nextThoughtNeeded": tool_input["nextThoughtNeeded"],
        }

        if "isRevision" in tool_input:
            thought_data["isRevision"] = tool_input["isRevision"]

        if "revisesThought" in tool_input:
            thought_data["revisesThought"] = tool_input["revisesThought"]

        if "branchFromThought" in tool_input:
            thought_data["branchFromThought"] = tool_input["branchFromThought"]

        if "branchId" in tool_input:
            thought_data["branchId"] = tool_input["branchId"]

        return thought_data

    def _format_thought(self, thought_data: ThoughtData) -> str:
        """
        Format a thought for display.

        Args:
            thought_data: The thought data

        Returns:
            Formatted thought string
        """
        thought = thought_data["thought"]
        thought_number = thought_data["thoughtNumber"]
        total_thoughts = thought_data["totalThoughts"]
        is_revision = thought_data.get("isRevision", False)
        revises_thought = thought_data.get("revisesThought")
        branch_from_thought = thought_data.get("branchFromThought")
        branch_id = thought_data.get("branchId")

        if is_revision:
            prefix = "🔄 Revision"
            context = f" (revising thought {revises_thought})"
        elif branch_from_thought:
            prefix = "🌿 Branch"
            context = f" (from thought {branch_from_thought}, ID: {branch_id})"
        else:
            prefix = "💭 Thought"
            context = ""

        header = f"{prefix} {thought_number}/{total_thoughts}{context}"

        return f"{header}: {thought}"

    def run(self, tool_input: Dict[str, Any]) -> str:
        """
        Run the sequential thinking tool.

        Args:
            tool_input: The input data for the tool

        Returns:
            Tool output as a string
        """
        try:
            # Validate input
            validated_input = self._validate_input(tool_input)

            # Add to thought history
            self.thought_history.append(validated_input)

            # Handle branches
            if validated_input.get("branchFromThought") and validated_input.get(
                "branchId"
            ):
                branch_id = validated_input["branchId"]
                if branch_id not in self.branches:
                    self.branches[branch_id] = []
                self.branches[branch_id].append(validated_input)

            # Format and log the thought
            formatted_thought = self._format_thought(validated_input)
            if self.verbose:
                logger.info(formatted_thought)

            # Prepare response
            response = {
                "thoughtNumber": validated_input["thoughtNumber"],
                "totalThoughts": validated_input["totalThoughts"],
                "nextThoughtNeeded": validated_input["nextThoughtNeeded"],
                "branches": list(self.branches.keys()),
                "thoughtHistoryLength": len(self.thought_history),
            }

            return json.dumps(response, indent=2)

        except Exception as e:
            error_message = f"Error in sequential thinking tool: {str(e)}"
            logger.error(error_message)
            return json.dumps({"error": error_message})

    def get_thought_history(self) -> List[ThoughtData]:
        """
        Get the thought history.

        Returns:
            List of thought data
        """
        return self.thought_history

    def get_branches(self) -> Dict[str, List[ThoughtData]]:
        """
        Get the branches.

        Returns:
            Dictionary of branch ID to list of thought data
        """
        return self.branches

    def get_last_thought(self) -> Optional[ThoughtData]:
        """
        Get the last thought.

        Returns:
            The last thought data, or None if there isn't one
        """
        if not self.thought_history:
            return None
        return self.thought_history[-1]

    def extract_cypher_queries(self) -> List[str]:
        """
        Extract Cypher queries from the thought history.

        Returns:
            List of Cypher queries
        """
        queries = []
        pattern = r"```cypher\s*(.*?)\s*```"

        for thought_data in self.thought_history:
            thought = thought_data["thought"]
            matches = re.findall(pattern, thought, re.DOTALL)
            queries.extend(matches)

        return queries


class MCPTools:
    """Wrapper for MCP client interactions"""

    def __init__(self, config: Dict[str, Any], agent=None):
        self.config = config
        self.agent = agent  # Store the Pydantic AI agent for LLM calls
        self.http_client = httpx.AsyncClient(
            timeout=config.get("article_fetch_timeout", 30),
            follow_redirects=True,
            headers={
                "User-Agent": "SecurityContentAnalyzer/1.0 (Security Research Tool)"
            },
        )

        # Legacy MCP client variables (kept for backward compatibility)
        self.sequential_client = None
        self.memgraph_client = None

        # Initialize local Sequential Thinking Tool
        self.sequential_thinking_tool = SequentialThinkingTool(verbose=True)

        # Initialize Memgraph connection if configured
        self.memgraph_connection = None
        self._init_memgraph_connection()

        # Sequential thinking state (for backward compatibility)
        self.current_thought_number = 0
        self.total_thoughts = 5  # Initial estimate
        self.thought_history = []

    def _init_memgraph_connection(self):
        """Initialize Memgraph connection if configured"""
        try:
            # Check if explicit Memgraph configuration is provided in config
            memgraph_uri = self.config.get("memgraph_uri")
            memgraph_username = self.config.get("memgraph_username")
            memgraph_password = self.config.get("memgraph_password")

            if memgraph_uri:
                # Explicit configuration provided
                try:
                    from neo4j import GraphDatabase

                    auth = None
                    if memgraph_username:
                        auth = (memgraph_username, memgraph_password)

                    self.memgraph_connection = GraphDatabase.driver(
                        memgraph_uri, auth=auth
                    )
                    logger.info(f"Memgraph connection initialized: {memgraph_uri}")
                    return
                except ImportError:
                    logger.warning(
                        "neo4j package not available - Memgraph functionality disabled"
                    )
                except Exception as e:
                    logger.warning(f"Failed to initialize Memgraph connection: {e}")

            # Fallback to MemgraphConfig for environment-based configuration
            memgraph_config = MemgraphConfig()
            if memgraph_config.uri and memgraph_config.uri != "bolt://localhost:7687":
                # Only initialize if we have a non-default URI (indicating it's configured)
                try:
                    from neo4j import GraphDatabase

                    self.memgraph_connection = GraphDatabase.driver(
                        memgraph_config.uri,
                        auth=(memgraph_config.username, memgraph_config.password),
                    )
                    logger.info(
                        f"Memgraph connection initialized: {memgraph_config.uri}"
                    )
                except ImportError:
                    logger.warning(
                        "neo4j package not available - Memgraph functionality disabled"
                    )
                except Exception as e:
                    logger.warning(f"Failed to initialize Memgraph connection: {e}")
            else:
                logger.info(
                    "Memgraph not configured - using fallback for Cypher queries"
                )
        except Exception as e:
            logger.warning(f"Error initializing Memgraph: {e}")

    async def initialize_mcp_clients(self):
        """Initialize tools (keeping method name for backward compatibility)"""
        logger.info("Initializing tools...")

        # Reset the sequential thinking tool for a fresh start
        self.sequential_thinking_tool.reset()

        logger.info("✅ Sequential Thinking Tool ready")
        logger.info(
            "✅ Memgraph connection configured"
            if self.memgraph_connection
            else "⚠️  Memgraph not configured - using simulation"
        )
        logger.info("Tools initialization completed")

    async def read_article(self, url: str) -> str:
        """Fetch and clean article content"""
        try:
            logger.info(f"Fetching article from: {url}")

            response = await self.http_client.get(url)
            response.raise_for_status()

            # Parse HTML content
            soup = BeautifulSoup(response.content, "html.parser")

            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                script.decompose()

            # Try to find main content areas
            main_content = None

            # Common content selectors
            content_selectors = [
                "article",
                '[role="main"]',
                ".content",
                ".post-content",
                ".entry-content",
                ".article-content",
                "main",
                "#content",
                ".main-content",
            ]

            for selector in content_selectors:
                elements = soup.select(selector)
                if elements:
                    main_content = elements[0]
                    break

            # Fallback to body if no main content found
            if main_content is None:
                main_content = soup.find("body") or soup

            # Extract text
            text = main_content.get_text(separator="\n", strip=True)

            # Clean up text
            text = re.sub(r"\n\s*\n", "\n\n", text)  # Remove excessive newlines
            text = re.sub(r"[ \t]+", " ", text)  # Normalize spaces

            # Limit content length
            max_length = self.config.get("max_content_length", 50000)
            if len(text) > max_length:
                text = text[:max_length] + "\n\n[Content truncated...]"

            logger.info(f"Successfully extracted {len(text)} characters from article")
            return text

        except httpx.HTTPError as e:
            error_msg = f"HTTP error fetching article: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Error processing article content: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)

    async def think_sequential(
        self,
        thought_type: str,
        prompt: str,
        context: Optional[Dict] = None,
        output_schema: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Execute structured thinking step via LLM agent"""

        # Use actual LLM for thinking instead of local simulation
        return await self._think_with_llm_agent(thought_type, prompt, context)

    async def _think_with_llm_agent(
        self, thought_type: str, prompt: str, context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Use actual LLM agent for structured thinking"""

        try:
            # Increment thought number
            self.current_thought_number += 1

            logger.info(
                f"🤖 LLM Sequential Thinking Step {self.current_thought_number}/{self.total_thoughts}: {thought_type}"
            )

            if not self.agent:
                logger.warning("No LLM agent available, falling back to local tool")
                return await self._think_with_local_tool(thought_type, prompt, context)

            # Create structured thinking prompt for the LLM
            thinking_prompt = f"""You are performing sequential thinking step {self.current_thought_number} of {self.total_thoughts}.

Thought Type: {thought_type.upper()}

Task: {prompt}

Context: {json.dumps(context, indent=2) if context else "None"}

Please think through this step carefully and provide:
1. Your reasoning process
2. Key insights or findings
3. Actionable conclusions based on the thought type

For assessment tasks, provide:
- has_sufficient_content: boolean
- can_generate_at: boolean (attack techniques)
- can_generate_dom: boolean (detection models)
- can_generate_pom: boolean (prevention models)
- confidence: float (0.0 to 1.0)
- reasoning: string
- missing_aspects: array of strings
- suggested_searches: array of strings

For generation tasks, provide specific definitions or recommendations.

Respond in a structured format that includes your reasoning and conclusions."""

            # Make the LLM call
            result = await self.agent.run(thinking_prompt)

            logger.info(
                f"✅ LLM thinking completed: {len(str(result.output))} chars response"
            )

            # Store the thought in history
            thought_data = {
                "thought_type": thought_type,
                "thought_number": self.current_thought_number,
                "total_thoughts": self.total_thoughts,
                "prompt": prompt,
                "context": context,
                "llm_response": str(result.output),
                "timestamp": json.dumps({"timestamp": "now"}),  # Simplified timestamp
            }
            self.thought_history.append(thought_data)

            # Extract conclusion based on thought type and LLM response
            conclusion = self._extract_conclusion_from_llm_response(
                thought_type, str(result.output), context
            )

            # Return structured response
            return {
                "thought_type": thought_type,
                "thought_number": self.current_thought_number,
                "total_thoughts": self.total_thoughts,
                "reasoning": str(result.output),
                "llm_response": str(result.output),
                "next_thought_needed": self.current_thought_number
                < self.total_thoughts,
                "conclusion": conclusion,
            }

        except Exception as e:
            logger.error(f"Error in LLM sequential thinking: {e}")
            # Fallback to local tool
            return await self._think_with_local_tool(thought_type, prompt, context)

    async def _think_with_local_tool(
        self, thought_type: str, prompt: str, context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Use local Sequential Thinking Tool for structured thinking"""

        try:
            # Increment thought number
            self.current_thought_number += 1

            # Create the thought content
            thought_content = f"[{thought_type.upper()}] {prompt}"
            if context:
                thought_content += f"\n\nContext: {json.dumps(context, indent=2)}"

            # Prepare tool input
            tool_input = {
                "thought": thought_content,
                "thoughtNumber": self.current_thought_number,
                "totalThoughts": self.total_thoughts,
                "nextThoughtNeeded": self.current_thought_number < self.total_thoughts,
                "isRevision": False,
            }

            logger.info(
                f"🧠 Sequential Thinking Step {self.current_thought_number}/{self.total_thoughts}: {thought_type}"
            )

            # Run the sequential thinking tool
            result_json = self.sequential_thinking_tool.run(tool_input)
            result_data = json.loads(result_json)

            # Extract conclusion based on thought type
            conclusion = self._extract_conclusion_from_thought(
                thought_type, thought_content
            )

            # Return structured response
            return {
                "thought_type": thought_type,
                "thought_number": self.current_thought_number,
                "total_thoughts": self.total_thoughts,
                "reasoning": thought_content,
                "tool_response": result_data,
                "next_thought_needed": result_data.get("nextThoughtNeeded", False),
                "conclusion": conclusion,
            }

        except Exception as e:
            logger.error(f"Error in local sequential thinking: {e}")
            # Final fallback to the old implementation
            return await self._think_fallback(thought_type, prompt, context)

    def _extract_conclusion_from_thought(
        self,
        thought_type: str,
        thought_content: str,
        mcp_response: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Extract actionable conclusions from the thought process"""

        # This is a simplified extraction - in a real implementation,
        # you might use additional LLM calls to parse the reasoning

        if thought_type == "assessment":
            return {
                "has_sufficient_content": True,
                "can_generate_at": "attack" in thought_content.lower()
                or "technique" in thought_content.lower(),
                "can_generate_dom": "detection" in thought_content.lower()
                or "monitor" in thought_content.lower(),
                "can_generate_pom": "prevention" in thought_content.lower()
                or "mitigation" in thought_content.lower(),
                "confidence": 0.8,
                "missing_aspects": [],
                "suggested_searches": [],
            }
        elif thought_type == "enrichment":
            return {
                "search_queries": ["MITRE ATT&CK techniques", "detection methods"],
                "sources_found": [],
            }
        elif thought_type == "generation":
            return {
                "definitions": {
                    "attack_technique": None,
                    "detection_model": None,
                    "prevention_model": None,
                }
            }
        else:
            return {"result": "processed"}

    def _extract_conclusion_from_llm_response(
        self, thought_type: str, llm_response: str, context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Extract actionable conclusions from LLM response"""

        try:
            # Try to parse JSON from LLM response first
            import json
            import re

            # Look for JSON-like structures in the response
            json_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
            json_matches = re.findall(json_pattern, llm_response, re.DOTALL)

            for json_match in json_matches:
                try:
                    parsed_data = json.loads(json_match)
                    if isinstance(parsed_data, dict):
                        # If we found valid JSON, use it
                        return parsed_data
                except json.JSONDecodeError:
                    continue

            # Fallback to text analysis if no valid JSON found
            response_lower = llm_response.lower()

            if thought_type == "assessment":
                return {
                    "has_sufficient_content": any(
                        word in response_lower
                        for word in ["sufficient", "adequate", "enough", "detailed"]
                    ),
                    "can_generate_at": any(
                        word in response_lower
                        for word in ["attack", "technique", "mitre", "tactics"]
                    ),
                    "can_generate_dom": any(
                        word in response_lower
                        for word in ["detection", "monitor", "alert", "signature"]
                    ),
                    "can_generate_pom": any(
                        word in response_lower
                        for word in ["prevention", "mitigation", "defense", "protect"]
                    ),
                    "confidence": 0.8 if "confident" in response_lower else 0.6,
                    "reasoning": llm_response[:500],
                    "missing_aspects": [],
                    "suggested_searches": [],
                }
            elif thought_type == "enrichment":
                return {
                    "search_queries": ["MITRE ATT&CK techniques", "detection methods"],
                    "sources_found": [],
                    "reasoning": llm_response[:500],
                }
            elif thought_type.startswith("generation"):
                return {
                    "definitions": {
                        "attack_technique": "attack" in response_lower,
                        "detection_model": "detection" in response_lower,
                        "prevention_model": "prevention" in response_lower,
                    },
                    "reasoning": llm_response[:500],
                }
            else:
                return {"result": "processed", "reasoning": llm_response[:500]}

        except Exception as e:
            logger.warning(f"Error extracting conclusion from LLM response: {e}")
            return {
                "result": "processed",
                "reasoning": llm_response[:500],
                "error": str(e),
            }

    async def execute_cypher(self, query: str, timeout: int = 5000) -> Dict[str, Any]:
        """Execute Cypher query via Memgraph or fallback simulation"""

        logger.info(f"Executing Cypher query: {query[:100]}...")
        start_time = asyncio.get_event_loop().time()

        try:
            # Basic query validation
            if not query.strip():
                return {
                    "success": False,
                    "error": "Empty query",
                    "data": None,
                    "execution_time_ms": 0,
                }

            # Check for basic Cypher syntax
            if not any(
                keyword in query.upper()
                for keyword in ["MATCH", "CREATE", "MERGE", "RETURN", "SHOW", "CALL"]
            ):
                return {
                    "success": False,
                    "error": "Invalid Cypher syntax - missing required keywords",
                    "data": None,
                    "execution_time_ms": 0,
                }

            # Try to execute with Memgraph if available
            if self.memgraph_connection:
                try:
                    with self.memgraph_connection.session() as session:
                        result = session.run(query)
                        data = result.data()

                    execution_time = int(
                        (asyncio.get_event_loop().time() - start_time) * 1000
                    )

                    logger.info(
                        f"Cypher query executed successfully, returned {len(data)} rows"
                    )
                    return {
                        "success": True,
                        "error": None,
                        "data": data,
                        "execution_time_ms": execution_time,
                        "row_count": len(data),
                    }

                except Exception as e:
                    logger.error(f"Memgraph query execution failed: {e}")
                    execution_time = int(
                        (asyncio.get_event_loop().time() - start_time) * 1000
                    )
                    return {
                        "success": False,
                        "error": f"Memgraph execution error: {str(e)}",
                        "data": None,
                        "execution_time_ms": execution_time,
                    }
            else:
                # Fallback simulation
                await asyncio.sleep(0.1)  # Simulate network delay
                execution_time = int(
                    (asyncio.get_event_loop().time() - start_time) * 1000
                )

                logger.info("Memgraph not available, simulating query execution")
                return {
                    "success": True,
                    "error": None,
                    "data": [],  # Empty result set for simulation
                    "execution_time_ms": execution_time,
                    "row_count": 0,
                    "note": "Simulated execution - Memgraph not configured",
                }

        except Exception as e:
            execution_time = int((asyncio.get_event_loop().time() - start_time) * 1000)
            return {
                "success": False,
                "error": str(e),
                "data": None,
                "execution_time_ms": execution_time,
            }

    async def search_web(
        self, query: str, max_results: int = 5
    ) -> List[Dict[str, Any]]:
        """Search web for additional context (placeholder)"""

        # Placeholder implementation for web search
        logger.info(f"Web search: {query}")

        # Return empty results for now
        return []

    async def cleanup(self):
        """Clean up resources"""
        await self.http_client.aclose()

        # Close Memgraph connection if it exists
        if hasattr(self, "memgraph_connection") and self.memgraph_connection:
            try:
                self.memgraph_connection.close()
                logger.info("Memgraph connection closed")
            except Exception as e:
                logger.warning(f"Error closing Memgraph connection: {e}")

        # Reset sequential thinking tool
        self.sequential_thinking_tool.reset()
        logger.info("Sequential Thinking Tool reset")

    def get_thought_history(self) -> List[Dict[str, Any]]:
        """Get the complete thought history for this session (backward compatibility)"""
        return self.thought_history.copy()

    def get_sequential_thought_history(self) -> List[ThoughtData]:
        """Get the sequential thinking tool's thought history"""
        return self.sequential_thinking_tool.get_thought_history()

    def get_sequential_branches(self) -> Dict[str, List[ThoughtData]]:
        """Get the sequential thinking tool's branches"""
        return self.sequential_thinking_tool.get_branches()

    def extract_cypher_queries_from_thoughts(self) -> List[str]:
        """Extract Cypher queries from the sequential thinking history"""
        return self.sequential_thinking_tool.extract_cypher_queries()

    def reset_thinking_session(self):
        """Reset the thinking session for a new analysis"""
        self.current_thought_number = 0
        self.total_thoughts = 5
        self.thought_history = []
        self.sequential_thinking_tool.reset()
