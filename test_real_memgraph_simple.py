#!/usr/bin/env python3
"""
Simple test to demonstrate real Memgraph integration with security queries
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from loguru import logger
from src.tools import MCPTools
from src.memgraph_schema import MemgraphSchemaLoader, CypherQueryBuilder


async def test_real_memgraph_simple():
    """Simple test of real Memgraph integration"""
    print("🚀 Simple Real Memgraph Integration Test")
    print("=" * 50)
    
    try:
        # Initialize tools with explicit Memgraph configuration
        tools_config = {
            "article_fetch_timeout": 60,
            "max_content_length": 50000,
            "cypher_timeout": 5000,
            "memgraph_uri": "bolt://localhost:7687",
            "memgraph_username": "",
            "memgraph_password": "",
            "mcp": {
                "sequential_thinking_path": "npx",
                "sequential_thinking_args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
                "memgraph_path": "python",
                "memgraph_args": [],
            },
        }
        
        tools = MCPTools(tools_config)
        await tools.initialize_mcp_clients()
        
        # Test 1: Basic connectivity
        print("\\n🔌 Testing Memgraph Connectivity...")
        result = await tools.execute_cypher("RETURN 'Real Memgraph Working!' as status, timestamp() as time")
        
        if result["success"] and not result.get("note"):
            print("   ✅ Real Memgraph connection successful!")
            data = result.get("data", [])
            if data:
                print(f"      Status: {data[0].get('status')}")
                print(f"      Time: {data[0].get('time')}")
        else:
            print("   ⚠️  Using simulation mode")
            print(f"      Note: {result.get('note', 'Unknown')}")
        
        # Test 2: Schema-aware queries
        print("\\n🗄️  Testing Schema-Aware Security Queries...")
        schema_loader = MemgraphSchemaLoader()
        query_builder = CypherQueryBuilder(schema_loader)
        security_queries = query_builder.build_security_analysis_queries()
        
        print(f"   Generated {len(security_queries)} security queries")
        
        # Test each query
        for i, query_data in enumerate(security_queries, 1):
            print(f"\\n   Query {i}: {query_data['name']}")
            
            result = await tools.execute_cypher(query_data['query'])
            
            if result["success"]:
                execution_time = result.get('execution_time_ms', 0)
                row_count = result.get('row_count', 0)
                is_real = not result.get('note')
                
                mode = "Real" if is_real else "Simulated"
                status = "✅" if is_real else "🔄"
                
                print(f"      {status} {mode} execution: {execution_time}ms, {row_count} rows")
                
                if is_real and row_count > 0:
                    data = result.get('data', [])
                    print(f"         Sample data: {data[0] if data else 'None'}")
                
            else:
                print(f"      ❌ Failed: {result.get('error', 'Unknown')}")
        
        # Test 3: Graph overview
        print("\\n📊 Getting Graph Overview...")
        overview_queries = [
            "MATCH (n) RETURN labels(n) as node_types, count(n) as count ORDER BY count DESC LIMIT 5",
            "MATCH ()-[r]->() RETURN type(r) as relationship_type, count(r) as count ORDER BY count DESC LIMIT 3",
            "CALL mg.procedures() YIELD name RETURN name LIMIT 3"
        ]
        
        for query in overview_queries:
            result = await tools.execute_cypher(query)
            if result["success"]:
                execution_time = result.get('execution_time_ms', 0)
                row_count = result.get('row_count', 0)
                is_real = not result.get('note')
                
                print(f"   {'✅' if is_real else '🔄'} Query executed: {execution_time}ms, {row_count} rows")
                
                if is_real and row_count > 0:
                    data = result.get('data', [])
                    for record in data[:3]:  # Show first 3 records
                        print(f"      - {record}")
            else:
                print(f"   ❌ Query failed: {result.get('error', 'Unknown')}")
        
        # Test 4: Create and query test data
        print("\\n🧪 Testing Data Creation and Querying...")
        
        # Create a test security finding
        create_query = """
        CREATE (sf:SecurityFinding_SecurityFinding {
            id: 'test-finding-001',
            description: 'Test security finding from agent integration test',
            severity: 'high',
            type: 'Test'
        })
        RETURN sf.id as created_id
        """
        
        result = await tools.execute_cypher(create_query)
        if result["success"] and not result.get('note'):
            print("   ✅ Created test security finding")
            data = result.get('data', [])
            if data:
                print(f"      Created ID: {data[0].get('created_id')}")
        else:
            print("   🔄 Simulated data creation")
        
        # Query the test data
        query_test_data = """
        MATCH (sf:SecurityFinding_SecurityFinding)
        WHERE sf.id = 'test-finding-001'
        RETURN sf.id, sf.description, sf.severity
        """
        
        result = await tools.execute_cypher(query_test_data)
        if result["success"]:
            execution_time = result.get('execution_time_ms', 0)
            row_count = result.get('row_count', 0)
            is_real = not result.get('note')
            
            print(f"   {'✅' if is_real else '🔄'} Test data query: {execution_time}ms, {row_count} rows")
            
            if is_real and row_count > 0:
                data = result.get('data', [])
                print(f"      Found test data: {data[0]}")
        
        # Cleanup test data
        cleanup_query = "MATCH (sf:SecurityFinding_SecurityFinding {id: 'test-finding-001'}) DELETE sf"
        await tools.execute_cypher(cleanup_query)
        
        # Final summary
        print("\\n" + "=" * 50)
        print("🎯 Integration Test Summary")
        print("=" * 50)
        
        if not result.get('note'):  # Real connection
            print("✅ REAL MEMGRAPH INTEGRATION SUCCESS!")
            print("   🔗 Connected to live Memgraph instance")
            print("   📊 Executed schema-aware security queries")
            print("   🧪 Created and queried test data")
            print("   ⚡ Full database functionality available")
        else:
            print("🔄 SIMULATION MODE ACTIVE")
            print("   📝 All queries executed in simulation")
            print("   ✅ Schema-aware query generation working")
            print("   💡 Start Memgraph for full functionality")
        
        # Cleanup
        await tools.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the simple integration test"""
    print("🧪 Security Content Analyzer - Simple Memgraph Test")
    print("Testing real database integration with security queries\\n")
    
    success = await test_real_memgraph_simple()
    
    if success:
        print("\\n✅ Simple integration test completed!")
    else:
        print("\\n❌ Integration test failed!")


if __name__ == "__main__":
    asyncio.run(main())
