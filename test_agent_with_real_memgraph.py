#!/usr/bin/env python3
"""
Test the SecurityAgent with real Memgraph connection
This test specifically configures the agent to use the running Memgraph instance
"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from loguru import logger
from src.config import load_config
from src.tools import MCPTools
from src.agent import SecurityAgent
from src.models import ProcessingStatus


async def test_agent_with_real_memgraph():
    """Test the agent with real Memgraph connection"""
    print("🚀 Testing SecurityAgent with REAL Memgraph Connection")
    print("=" * 70)
    
    try:
        # Load the test article content
        test_article_path = Path("test_article.txt")
        if not test_article_path.exists():
            print("❌ test_article.txt not found!")
            return False
            
        with open(test_article_path, 'r', encoding='utf-8') as f:
            article_content = f.read()
        
        print(f"📄 Loaded test article: {len(article_content)} characters")
        
        # Load configuration and force Memgraph connection
        print("🔧 Loading configuration with Memgraph enabled...")
        config = load_config()
        
        # Force Memgraph configuration
        config.memgraph_uri = "bolt://localhost:7687"
        config.memgraph_username = ""
        config.memgraph_password = ""
        
        # Initialize tools with explicit Memgraph configuration
        print("🛠️  Initializing tools with Memgraph connection...")
        tools_config = {
            "article_fetch_timeout": config.processing.article_fetch_timeout,
            "max_content_length": config.processing.max_content_length,
            "cypher_timeout": config.validation.cypher_timeout,
            "memgraph_uri": config.memgraph_uri,
            "memgraph_username": config.memgraph_username,
            "memgraph_password": config.memgraph_password,
            "mcp": {
                "sequential_thinking_path": config.mcp.sequential_thinking_path,
                "sequential_thinking_args": config.mcp.sequential_thinking_args,
                "memgraph_path": config.mcp.memgraph_path,
                "memgraph_args": config.mcp.memgraph_args,
            },
        }
        
        tools = MCPTools(tools_config)
        await tools.initialize_mcp_clients()
        
        # Test Memgraph connectivity first
        print("🔌 Testing Memgraph connectivity...")
        test_result = await tools.execute_cypher("RETURN 'Hello from real Memgraph!' as message")
        
        if test_result["success"] and not test_result.get("note"):
            print("   ✅ Real Memgraph connection established!")
            print(f"      Message: {test_result.get('data', [{}])[0] if test_result.get('data') else 'Connected'}")
        else:
            print("   ⚠️  Using simulation mode - Memgraph not available")
            print(f"      Note: {test_result.get('note', 'Unknown')}")
        
        # Initialize agent
        print("🤖 Initializing SecurityAgent...")
        agent = SecurityAgent(config, tools)
        
        # Test schema-aware queries with real data
        print("\\n" + "="*70)
        print("🗄️  Testing Schema-Aware Queries with Real Data")
        print("="*70)
        
        from src.memgraph_schema import CypherQueryBuilder
        query_builder = CypherQueryBuilder(agent.schema_loader)
        security_queries = query_builder.build_security_analysis_queries()
        
        print(f"🔍 Testing {len(security_queries)} security analysis queries...")
        
        real_data_found = False
        for i, query_data in enumerate(security_queries, 1):
            print(f"\\n   Query {i}: {query_data['name']}")
            print(f"   Description: {query_data['description']}")
            
            result = await tools.execute_cypher(query_data['query'])
            
            if result["success"]:
                execution_time = result.get('execution_time_ms', 0)
                row_count = result.get('row_count', 0)
                is_simulation = result.get('note') and 'simulation' in result.get('note', '').lower()
                
                status = "🔄" if is_simulation else "✅"
                mode = "Simulated" if is_simulation else "Real"
                
                print(f"   {status} {mode} execution: {execution_time}ms, {row_count} rows")
                
                if not is_simulation and row_count > 0:
                    real_data_found = True
                    data = result.get('data', [])
                    print(f"      📊 Sample results:")
                    for j, record in enumerate(data[:2]):  # Show first 2 records
                        print(f"         {j+1}. {record}")
                    if len(data) > 2:
                        print(f"         ... and {len(data) - 2} more records")
                
            else:
                print(f"   ❌ Query failed: {result.get('error', 'Unknown error')}")
        
        # Run full analysis workflow
        print("\\n" + "="*70)
        print("🔄 Full Analysis Workflow with Real Memgraph")
        print("="*70)
        
        # Mock the read_article method to return our test content
        test_url = "file://test_article.txt"
        original_read_article = tools.read_article
        async def mock_read_article(url):
            if url == test_url:
                return article_content
            return await original_read_article(url)
        tools.read_article = mock_read_article
        
        print(f"🚀 Running full analysis with {'real' if not test_result.get('note') else 'simulated'} Memgraph...")
        start_time = datetime.now()
        
        # Run the full analysis
        result = await agent._analyze_article_content(test_url)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"\\n📊 ANALYSIS RESULTS:")
        print(f"   Status: {result.status}")
        print(f"   Processing time: {processing_time:.2f} seconds")
        print(f"   Errors: {len(result.errors)}")
        print(f"   Warnings: {len(result.warnings)}")
        
        # Check Cypher query execution details
        if result.attack_technique and result.attack_technique.cypher_queries:
            print(f"\\n🔍 Cypher Query Execution Details:")
            real_executions = 0
            simulated_executions = 0
            
            for query in result.attack_technique.cypher_queries:
                if hasattr(query, 'validation_status'):
                    if query.validation_status == "valid":
                        if hasattr(query, 'execution_time_ms') and query.execution_time_ms:
                            real_executions += 1
                            print(f"   ✅ {query.name}: {query.execution_time_ms}ms (real)")
                        else:
                            simulated_executions += 1
                            print(f"   🔄 {query.name}: simulated")
                    else:
                        print(f"   ❌ {query.name}: {query.validation_status}")
            
            print(f"   📈 Execution summary: {real_executions} real, {simulated_executions} simulated")
        
        # Save enhanced results
        print("\\n" + "="*70)
        print("💾 Saving Enhanced Test Results")
        print("="*70)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"test_results_real_memgraph_{timestamp}.json"
        
        result_dict = {
            "test_metadata": {
                "timestamp": timestamp,
                "test_type": "real_memgraph_integration",
                "article_source": "test_article.txt",
                "article_length": len(article_content),
                "processing_time_seconds": processing_time,
                "memgraph_available": not bool(test_result.get('note')),
                "real_data_found": real_data_found,
            },
            "memgraph_connectivity": {
                "connection_successful": test_result["success"],
                "execution_mode": "real" if not test_result.get('note') else "simulation",
                "test_query_time_ms": test_result.get('execution_time_ms', 0),
            },
            "security_queries_tested": len(security_queries),
            "workflow_results": {
                "status": result.status.value,
                "definitions_generated": sum([
                    1 if result.attack_technique else 0,
                    1 if result.detection_model else 0,
                    1 if result.prevention_model else 0,
                ]),
                "cypher_queries_count": len(result.attack_technique.cypher_queries) if result.attack_technique else 0,
                "errors": result.errors,
                "warnings": result.warnings,
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(result_dict, f, indent=2)
        
        print(f"✅ Enhanced test results saved to: {output_file}")
        
        # Final assessment
        print("\\n" + "="*70)
        print("🎯 FINAL ASSESSMENT - Real Memgraph Integration")
        print("="*70)
        
        success_criteria = {
            "Memgraph Connection": test_result["success"],
            "Schema Loading": agent.schema_loader.node_types is not None and len(agent.schema_loader.node_types) > 0,
            "Query Generation": result.attack_technique and len(result.attack_technique.cypher_queries) > 0,
            "Query Execution": result.attack_technique and all(
                hasattr(q, 'validation_status') and q.validation_status in ['valid', 'error'] 
                for q in result.attack_technique.cypher_queries
            ),
            "Full Workflow": result.status == ProcessingStatus.COMPLETE,
            "No Critical Errors": len(result.errors) == 0,
        }
        
        passed_tests = sum(success_criteria.values())
        total_tests = len(success_criteria)
        
        print(f"📊 Integration Test Results: {passed_tests}/{total_tests} criteria met")
        
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
        
        # Special notes about Memgraph integration
        if not test_result.get('note'):
            print(f"\\n🎉 REAL MEMGRAPH INTEGRATION SUCCESS!")
            print(f"   ✅ Connected to live Memgraph instance")
            print(f"   ✅ Executed schema-aware queries against real data")
            print(f"   ✅ Full workflow with database validation")
            if real_data_found:
                print(f"   ✅ Found and analyzed real security data in the database")
        else:
            print(f"\\n⚠️  SIMULATION MODE (Memgraph not configured)")
            print(f"   🔄 All queries executed in simulation mode")
            print(f"   ✅ Full workflow completed successfully")
            print(f"   💡 To enable real Memgraph: ensure connection parameters are correct")
        
        # Cleanup
        await tools.cleanup()
        
        return passed_tests >= 5  # At least 5/6 criteria should pass
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the real Memgraph integration test"""
    print("🧪 Security Content Analyzer - Real Memgraph Integration Test")
    print("Testing with live Memgraph database connection")
    print("\\n")
    
    success = await test_agent_with_real_memgraph()
    
    if success:
        print("\\n✅ Real Memgraph integration test completed successfully!")
        sys.exit(0)
    else:
        print("\\n❌ Some integration tests failed. Check the output above for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
