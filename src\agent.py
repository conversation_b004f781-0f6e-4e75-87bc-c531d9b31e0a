"""Main SecurityAgent class using Pydantic AI"""

import asyncio
import json
import yaml
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from loguru import logger

# Pydantic AI imports
from pydantic_ai import Agent, RunContext
import logfire

logfire.configure(send_to_logfire=True)

# Local imports
from .models import (
    ProcessingResult,
    ProcessingStatus,
    AssessmentResult,
    AttackTechnique,
    DetectionModel,
    PreventionModel,
    CypherQuery,
    EnrichmentSource,
    StateLog,
)
from .tools import MCPTools
from .config import Config, get_model_string, get_model_kwargs
from .memgraph_schema import MemgraphSchemaLoader, get_cypher_prompt_context


class SecurityAgent:
    """Main security content analysis agent"""

    def __init__(self, config: Config, mcp_tools: MCPTools):
        self.config = config
        self.tools = mcp_tools

        # Initialize Memgraph schema loader
        self.schema_loader = MemgraphSchemaLoader()

        # Initialize Logfire if token is available
        if config.logfire_token:
            try:
                logfire.configure(token=config.logfire_token)
                logger.info("Logfire configured")
            except Exception as e:
                logger.warning(f"Failed to configure Logfire: {e}")

        # Create main agent with provider-specific configuration
        model_string = get_model_string(config)
        model_kwargs = get_model_kwargs(config)

        logger.info(
            f"Initializing LLM: {config.llm.provider} provider with model {config.llm.model}"
        )

        self.agent = Agent(
            model=model_string,
            system_prompt=self._build_system_prompt(),
            retries=config.llm.retry_attempts,
        )

        # Pass the agent to tools so they can make LLM calls
        self.tools.agent = self.agent

    def _build_system_prompt(self) -> str:
        """Build the system prompt for the agent"""

        # Get Memgraph schema context
        cypher_context = get_cypher_prompt_context(self.schema_loader)

        return f"""You are a security content analyzer that extracts structured
        attack techniques, detection models, and prevention models from security articles.

        Your role is to:
        1. Assess article content for security relevance and completeness
        2. Enrich content with additional context when needed
        3. Generate structured definitions for Attack Techniques (AT), Detection Models (DoM), and Prevention Models (PoM)
        4. Validate any Cypher queries against a graph database

        Use sequential thinking for all analysis steps. Be thorough but efficient.
        Focus on extracting actionable security intelligence from the content.

        When generating definitions:
        - Attack Techniques should include MITRE ATT&CK mappings when possible
        - Detection Models should specify data sources and detection logic
        - Prevention Models should include implementation steps and complexity
        - All Cypher queries must be syntactically valid and schema-compliant

        {cypher_context}

        When generating Cypher queries, always:
        - Use the exact node labels from the schema above
        - Include appropriate WHERE clauses for filtering
        - Add LIMIT clauses to prevent large result sets
        - Focus on security analysis patterns
        - Validate syntax before including in definitions

        Always provide confidence scores and reasoning for your decisions."""

    async def _assess_content(self, content: str) -> AssessmentResult:
        """Assess article content for sufficiency and potential"""

        try:
            # Use direct LLM call for assessment instead of sequential thinking
            logger.info("🤖 Making direct LLM call for content assessment...")

            assessment_prompt = f"""Analyze this security article content and provide a structured assessment.

Article Content (first 2000 chars):
{content[:2000]}

Please assess:
1. Does this content have sufficient technical detail for generating security definitions?
2. Can we generate Attack Techniques (AT) from this content?
3. Can we generate Detection Models (DoM) from this content?
4. Can we generate Prevention Models (PoM) from this content?

Respond with a JSON object containing:
- has_sufficient_content: boolean
- can_generate_at: boolean
- can_generate_dom: boolean
- can_generate_pom: boolean
- confidence: float (0.0 to 1.0)
- reasoning: string explaining your assessment
- missing_aspects: array of strings describing what's missing
- suggested_searches: array of strings for additional research

Content length: {len(content)} characters"""

            # Make direct LLM call using Pydantic AI agent
            result = await self.agent.run(assessment_prompt)
            logger.info(
                f"✅ LLM assessment completed: {len(str(result.output))} chars response"
            )

            # Try to parse JSON response
            try:
                import json

                assessment_data = json.loads(result.output)
            except (json.JSONDecodeError, AttributeError):
                # Fallback parsing if LLM doesn't return valid JSON
                logger.warning(
                    "LLM didn't return valid JSON, using fallback assessment"
                )
                assessment_data = {
                    "has_sufficient_content": "attack" in content.lower()
                    or "technique" in content.lower(),
                    "can_generate_at": "attack" in content.lower()
                    or "mitre" in content.lower(),
                    "can_generate_dom": "detection" in content.lower()
                    or "monitor" in content.lower(),
                    "can_generate_pom": "prevention" in content.lower()
                    or "mitigation" in content.lower(),
                    "confidence": 0.7,
                    "reasoning": f"Fallback assessment based on content analysis. LLM response: {str(result.output)[:200]}...",
                    "missing_aspects": [],
                    "suggested_searches": [],
                }

            return AssessmentResult(
                has_sufficient_content=assessment_data.get(
                    "has_sufficient_content", False
                ),
                can_generate_at=assessment_data.get("can_generate_at", False),
                can_generate_dom=assessment_data.get("can_generate_dom", False),
                can_generate_pom=assessment_data.get("can_generate_pom", False),
                missing_aspects=assessment_data.get("missing_aspects", []),
                suggested_searches=assessment_data.get("suggested_searches", []),
                confidence=assessment_data.get("confidence", 0.5),
                reasoning=assessment_data.get("reasoning", str(result.output)[:500]),
            )

        except Exception as e:
            logger.error(f"Error in content assessment: {e}")
            return AssessmentResult(
                has_sufficient_content=False,
                can_generate_at=False,
                can_generate_dom=False,
                can_generate_pom=False,
                confidence=0.0,
                reasoning=f"Assessment failed: {e}",
            )

    async def _enrich_content(
        self, result: ProcessingResult, search_queries: List[str]
    ) -> None:
        """Enrich content with additional context"""

        max_rounds = self.config.processing.max_enrichment_rounds

        for round_num in range(1, max_rounds + 1):
            if round_num > len(search_queries):
                break

            try:
                query = search_queries[round_num - 1]
                logger.info(f"Enrichment round {round_num}: {query}")

                # Search for additional context
                search_results = await self.tools.search_web(query, max_results=3)

                for search_result in search_results:
                    source = EnrichmentSource(
                        url=search_result.get("url", ""),
                        title=search_result.get("title", ""),
                        content_snippet=search_result.get("snippet", ""),
                        relevance_score=search_result.get("relevance", 0.5),
                    )
                    result.enrichment_sources.append(source)

                result.enrichment_rounds = round_num

            except Exception as e:
                logger.warning(f"Enrichment round {round_num} failed: {e}")
                result.warnings.append(f"Enrichment round {round_num} failed: {e}")

    async def _generate_definitions(
        self, result: ProcessingResult, content: str, assessment: AssessmentResult
    ) -> None:
        """Generate security definitions based on content"""

        try:
            # Generate Attack Technique if applicable
            if assessment.can_generate_at:
                result.attack_technique = await self._generate_attack_technique(content)

            # Generate Detection Model if applicable
            if assessment.can_generate_dom:
                result.detection_model = await self._generate_detection_model(content)

            # Generate Prevention Model if applicable
            if assessment.can_generate_pom:
                result.prevention_model = await self._generate_prevention_model(content)

        except Exception as e:
            logger.error(f"Error generating definitions: {e}")
            result.errors.append(f"Definition generation failed: {e}")

    async def _generate_attack_technique(
        self, content: str
    ) -> Optional[AttackTechnique]:
        """Generate Attack Technique definition using schema template"""

        try:
            # Get the schema template
            schema_template = self.config.schemas.attack_technique_example

            if not schema_template:
                logger.warning("No attack technique schema template found")
                return None

            # Parse the YAML template
            template_data = yaml.safe_load(schema_template)

            # Use sequential thinking to generate the attack technique
            thinking_result = await self.tools.think_sequential(
                thought_type="generation_at",
                prompt=f"""Generate an Attack Technique definition based on this article content:

Article Content (first 1000 chars):
{content[:1000]}

Use this template as a guide:
{schema_template}

Extract and adapt the relevant information from the article to create a realistic Attack Technique definition.
Focus on technical details, prerequisites, and MITRE ATT&CK mappings where applicable.""",
                context={"template": template_data, "content_length": len(content)},
            )

            # For now, use the template data with some modifications based on content
            # In a real implementation, this would use LLM to extract and adapt

            # Create cypher queries based on template and schema
            cypher_queries = []
            if "cypher_queries" in template_data:
                for query_data in template_data["cypher_queries"]:
                    cypher_queries.append(
                        CypherQuery(
                            name=query_data.get("name", "Generated query"),
                            description=query_data.get(
                                "description", "Auto-generated query"
                            ),
                            query=query_data.get(
                                "query", "MATCH (n) RETURN n LIMIT 10"
                            ),
                        )
                    )

            # Add schema-aware security analysis queries
            from .memgraph_schema import CypherQueryBuilder

            query_builder = CypherQueryBuilder(self.schema_loader)
            security_queries = query_builder.build_security_analysis_queries()

            # Add a few relevant security queries to the attack technique
            for query_data in security_queries[:2]:  # Add first 2 security queries
                cypher_queries.append(
                    CypherQuery(
                        name=query_data["name"],
                        description=query_data["description"],
                        query=query_data["query"],
                    )
                )

            return AttackTechnique(
                name=template_data.get("name", "Generated Attack Technique"),
                description=template_data.get(
                    "description", "Generated from article content"
                ),
                mitre_tactics=template_data.get("mitre_tactics", ["Initial Access"]),
                mitre_technique_id=template_data.get("mitre_technique_id", "T1000"),
                prerequisites=template_data.get("prerequisites", []),
                target_systems=template_data.get(
                    "target_systems", ["Windows", "Linux"]
                ),
                cypher_queries=cypher_queries,
                confidence_score=template_data.get("confidence_score", 0.8),
            )

        except Exception as e:
            logger.error(f"Error generating attack technique: {e}")
            return None

    async def _generate_detection_model(self, content: str) -> Optional[DetectionModel]:
        """Generate Detection Model definition using schema template"""

        try:
            # Get the schema template
            schema_template = self.config.schemas.detection_model_example

            if not schema_template:
                logger.warning("No detection model schema template found")
                return None

            # Parse the YAML template
            template_data = yaml.safe_load(schema_template)

            # Use sequential thinking to generate the detection model
            thinking_result = await self.tools.think_sequential(
                thought_type="generation_dom",
                prompt=f"""Generate a Detection Model definition based on this article content:

Article Content (first 1000 chars):
{content[:1000]}

Use this template as a guide:
{schema_template}

Extract and adapt the relevant information from the article to create a realistic Detection Model definition.
Focus on detection logic, data sources, and expected outputs.""",
                context={"template": template_data, "content_length": len(content)},
            )

            return DetectionModel(
                name=template_data.get("name", "Generated Detection Model"),
                description=template_data.get(
                    "description", "Generated detection logic"
                ),
                detection_logic=template_data.get(
                    "detection_logic", "Monitor for suspicious activities"
                ),
                data_sources=template_data.get("data_sources", ["System logs"]),
                detection_type=template_data.get("detection_type", "real-time"),
                expected_output=template_data.get("expected_output", "Security alert"),
                false_positive_rate=template_data.get("false_positive_rate"),
                confidence_score=template_data.get("confidence_score", 0.8),
            )

        except Exception as e:
            logger.error(f"Error generating detection model: {e}")
            return None

    async def _generate_prevention_model(
        self, content: str
    ) -> Optional[PreventionModel]:
        """Generate Prevention Model definition using schema template"""

        try:
            # Get the schema template
            schema_template = self.config.schemas.prevention_model_example

            if not schema_template:
                logger.warning("No prevention model schema template found")
                return None

            # Parse the YAML template
            template_data = yaml.safe_load(schema_template)

            # Use sequential thinking to generate the prevention model
            thinking_result = await self.tools.think_sequential(
                thought_type="generation_pom",
                prompt=f"""Generate a Prevention Model definition based on this article content:

Article Content (first 1000 chars):
{content[:1000]}

Use this template as a guide:
{schema_template}

Extract and adapt the relevant information from the article to create a realistic Prevention Model definition.
Focus on prevention steps, implementation complexity, and target platforms.""",
                context={"template": template_data, "content_length": len(content)},
            )

            return PreventionModel(
                name=template_data.get("name", "Generated Prevention Model"),
                description=template_data.get(
                    "description", "Generated prevention steps"
                ),
                prevention_steps=template_data.get(
                    "prevention_steps", ["Implement security controls"]
                ),
                implementation_complexity=template_data.get(
                    "implementation_complexity", "medium"
                ),
                prerequisites=template_data.get("prerequisites", []),
                target_platforms=template_data.get(
                    "target_platforms", ["Windows", "Linux", "macOS"]
                ),
                effectiveness_rating=template_data.get("effectiveness_rating"),
                confidence_score=template_data.get("confidence_score", 0.8),
            )

        except Exception as e:
            logger.error(f"Error generating prevention model: {e}")
            return None

    async def _validate_cypher_queries(self, result: ProcessingResult) -> None:
        """Validate Cypher queries in generated definitions"""

        queries_to_validate = []

        # Collect all Cypher queries
        if result.attack_technique:
            queries_to_validate.extend(result.attack_technique.cypher_queries)

        # Validate each query
        for query in queries_to_validate:
            try:
                validation_result = await self.tools.execute_cypher(
                    query.query, timeout=self.config.validation.cypher_timeout
                )

                if validation_result["success"]:
                    query.validation_status = "valid"
                    query.execution_time_ms = validation_result.get("execution_time_ms")
                else:
                    query.validation_status = "invalid"
                    query.validation_error = validation_result.get("error")
                    result.warnings.append(f"Invalid Cypher query: {query.name}")

            except Exception as e:
                query.validation_status = "error"
                query.validation_error = str(e)
                result.warnings.append(f"Cypher validation error for {query.name}: {e}")

    async def process_articles(self, urls: List[str]) -> List[ProcessingResult]:
        """Process multiple articles"""

        results = []

        for url in urls:
            try:
                # Directly call the analysis method
                processing_result = await self._analyze_article_content(url)
                results.append(processing_result)

            except Exception as e:
                logger.error(f"Failed to process article {url}: {e}")

                # Create failed result
                failed_result = ProcessingResult(
                    url=url,
                    status=ProcessingStatus.FAILED,
                    started_at=datetime.now(timezone.utc),
                    completed_at=datetime.now(timezone.utc),
                    errors=[str(e)],
                )
                results.append(failed_result)

        return results

    async def _analyze_article_content(self, article_url: str) -> ProcessingResult:
        """Main analysis workflow using Sequential Thinking"""

        # Reset thinking session for this article
        self.tools.reset_thinking_session()

        result = ProcessingResult(
            url=article_url,
            status=ProcessingStatus.PENDING,
            started_at=datetime.now(timezone.utc),
        )

        try:
            # Step 1: Read article content
            logger.info(f"Starting analysis of: {article_url}")
            result.status = ProcessingStatus.ASSESSING

            article_content = await self.tools.read_article(article_url)

            # Step 2: Assess content sufficiency
            assessment_result = await self._assess_content(article_content)
            result.assessment = assessment_result

            if not assessment_result.has_sufficient_content:
                result.status = ProcessingStatus.FAILED
                result.errors.append("Insufficient content for analysis")
                return result

            # Step 3: Enrich content if needed
            if assessment_result.suggested_searches:
                result.status = ProcessingStatus.ENRICHING
                await self._enrich_content(result, assessment_result.suggested_searches)

            # Step 4: Generate applicable definitions
            result.status = ProcessingStatus.GENERATING
            await self._generate_definitions(result, article_content, assessment_result)

            # Step 5: Validate Cypher queries
            result.status = ProcessingStatus.VALIDATING
            await self._validate_cypher_queries(result)

            # Complete processing
            result.status = ProcessingStatus.COMPLETE
            result.completed_at = datetime.now(timezone.utc)

            if result.started_at and result.completed_at:
                result.processing_time_seconds = (
                    result.completed_at - result.started_at
                ).total_seconds()

            # Capture thought history
            result.thought_history = self.tools.get_thought_history()

            logger.info(f"Successfully completed analysis of: {article_url}")
            logger.info(
                f"💭 Generated {len(result.thought_history)} thoughts during analysis"
            )
            return result

        except Exception as e:
            logger.error(f"Error analyzing article {article_url}: {e}")
            result.status = ProcessingStatus.FAILED
            result.errors.append(str(e))
            result.completed_at = datetime.now(timezone.utc)
            return result
